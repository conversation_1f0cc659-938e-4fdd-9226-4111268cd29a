<!--
  projects.html — Projects App for Windows XP Simulation
  Provides the My Projects window content and structure.
  Loaded as an iframe in the main shell.
  @file src/apps/projects/projects.html
-->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>My Projects</title>
    <link rel="stylesheet" href="https://unpkg.com/xp.css@0.2.6/dist/XP.css" crossorigin="anonymous" />
    <link rel="stylesheet" href="projects.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Wix+Madefor+Display:wght@400;500;600;700;800&family=Work+Sans:wght@500;700&display=swap" rel="stylesheet" />
  </head>
  <body>
    <!-- Aurora Background -->
    <div class="aurora">
      <div class="aurora-blob blob1"></div>
      <div class="aurora-blob blob2"></div>
      <div class="aurora-blob blob3"></div>
      <div class="aurora-blob blob4"></div>
      <div class="aurora-blob blob5"></div>
    </div>

    <!-- Work Type Labels -->
    <div class="work-labels">
      <div class="work-label work-label--left">Client Work</div>
      <div class="work-label work-label--right">Personal Work</div>
    </div>

    <!-- Main Grid View -->
    <main id="projects-grid-view" class="projects-grid">
      <!-- Projects will be dynamically generated directly here -->
    </main>

    <!-- Project Detail Page -->
    <div id="project-detail-page" class="project-detail-page">
      <div class="aurora">
        <div class="aurora-blob blob1"></div>
        <div class="aurora-blob blob2"></div>
        <div class="aurora-blob blob3"></div>
        <div class="aurora-blob blob4"></div>
        <div class="aurora-blob blob5"></div>
      </div>

      <div class="scroll-indicator">
        <div class="scroll-dot"></div>
      </div>

      <div class="detail-content">
        <div class="detail-container">
          <!-- Project content will be populated here -->
        </div>
      </div>
    </div>

    <script type="module" src="projects.js"></script>
  </body>
</html>
