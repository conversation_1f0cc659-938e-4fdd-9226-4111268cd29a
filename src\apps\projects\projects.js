/*
  projects.js — Projects App Interactivity for Windows XP Simulation
  Handles dynamic loading of project data from projects.json, masonry layout for the project feed,
  project detail page navigation with fade transitions, click handling to open project details,
  video playback control based on visibility and window state, and communication with the parent
  window for toolbar navigation and maximized state changes.
  @file src/apps/projects/projects.js
*/



// ===== Utility Functions ===== //

/**
 * Converts a relative asset path to an absolute path for iframe context.
 * @param {string} path - The original asset path.
 * @returns {string} The transformed asset path.
 */
function toAbsoluteAssetPath(path) {
  if (!path) return path;
  if (path.startsWith("http:") || path.startsWith("https:")) return path;
  if (path.startsWith("../")) return path;
  return "../../../" + path;
}

/**
 * Check if the current device is mobile
 * @returns {boolean} True if mobile device
 */
function isMobileDevice() {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}

// ===== Module Imports ===== //
// import { EVENTS } from "../../scripts/utils/eventBus.js"; // Commented out as EVENTS is unused
import { isMobileDevice } from "../../scripts/utils/device.js"; // Utility to detect if the current device is mobile.

// ===== Utility Functions & Global State ===== //

/**
 * Debounces a function, ensuring it's only called after a certain delay
 * since the last time it was invoked.
 * @param {Function} func - The function to debounce.
 * @param {number} delay - The debounce delay in milliseconds.
 * @returns {Function} The debounced function.
 */
/* // Commented out as debounce is unused
function debounce(func, delay) {
  let timeoutId;
  return function(...args) {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
}
*/

// Global state for project navigation
let allPostsData = []; // Stores all project data extracted from post elements

/**
 * Helper function to create a DOM element with a given tag, class, and text content.
 * @param {string} tag - The HTML tag for the element.
 * @param {string} [className] - Optional CSS class name(s) for the element.
 * @param {string} [text] - Optional text content for the element.
 * @returns {HTMLElement} The created DOM element.
 */
function createEl(tag, className, text) {
  const el = document.createElement(tag);
  if (className) el.className = className;
  if (text !== undefined) el.textContent = text;
  return el;
}
/**
 * Sends a message to the parent window (main shell) if this script is running in an iframe.
 * @param {object} payload - The data to send to the parent window.
 */
function sendMessageToParent(payload) {
  if (window.parent && window.parent !== window) {
    window.parent.postMessage(payload, "*");
  }
}
/**
 * Transforms a relative asset path to be absolute from the root, suitable for use within an iframe.
 * If the path is already absolute (http/https) or correctly relative for iframe context (../),
 * it's returned unchanged. Otherwise, it's prefixed with '../../../'.
 * @param {string} path - The original asset path.
 * @returns {string} The transformed asset path, or the original if no transformation is needed.
 */
function toAbsoluteAssetPath(path) {
  // Keep for feed generation
  if (!path) return path;
  if (path.startsWith("http:") || path.startsWith("https:")) return path;
  if (path.startsWith("../")) return path;
  return "../../../" + path;
}

/**
 * Calculates the scrollbar width and sets it as a CSS custom property
 */
function setScrollbarWidth() {
  // Create a temporary element to measure scrollbar width
  const outer = document.createElement('div');
  outer.style.visibility = 'hidden';
  outer.style.overflow = 'scroll';
  outer.style.msOverflowStyle = 'scrollbar'; // needed for WinJS apps
  document.body.appendChild(outer);

  const inner = document.createElement('div');
  outer.appendChild(inner);

  const scrollbarWidth = outer.offsetWidth - inner.offsetWidth;
  outer.parentNode.removeChild(outer);

  // Set the scrollbar width as a CSS custom property
  document.documentElement.style.setProperty('--scrollbar-width', `${scrollbarWidth}px`);

  return scrollbarWidth;
}












// ===== DOMContentLoaded: Main Initialization ===== //
document.addEventListener("DOMContentLoaded", async () => {
  // Calculate and set scrollbar width for proper layout
  setScrollbarWidth();

  const feedContainer = document.querySelector(".feed-container");
  if (!feedContainer) {
    console.error("Feed container not found.");
    return;
  }

  let projects = [];
  try {
    // Fetch project data from the external projects.json file.
    const response = await fetch("../../../projects.json");
    projects = await response.json();
  } catch (e) {
    console.error("Failed to load or parse projects.json", e); // Added console error
    return;
  }

  /**
   * Creates an HTML element for a single project post based on the project data.
   * This includes setting up various data attributes for styling and interaction.
   * @param {object} project - The project data object.
   * @param {number} idx - The index of the project in the projects array.
   * @returns {HTMLDivElement} The created project post element.
   */
  function createPostElement(project, idx) {
    const post = document.createElement("div");
    post.className = `post ${project.type}-post`; // Base class + type-specific class (e.g., image-post, video-post)
    post.dataset.type = project.type; // 'image' or 'video'
    post.dataset.title = project.title;

    const mobileDevice = isMobileDevice(); // Check once for mobile-specific asset selection.

    // --- Logic for populating dataset attributes based on project type --- //
    if (project.type === "video") {
      // Set dataset attributes for mobile video sources if they exist
      if (project.srcMobile) {
        post.dataset.srcMobile = toAbsoluteAssetPath(project.srcMobile);
      }
      if (project.fullVideoSrcMobile) {
        post.dataset.fullVideoSrcMobile = toAbsoluteAssetPath(
          project.fullVideoSrcMobile,
        );
      }
      // Set dataset.src for the grid video thumbnail, preferring mobile source if available.
      post.dataset.src =
        mobileDevice && project.srcMobile
          ? toAbsoluteAssetPath(project.srcMobile)
          : toAbsoluteAssetPath(project.src);

      // Additional media items are stored in the images dataset for project detail view
      // Store full video source if available
      if (project.fullVideoSrc) {
        post.dataset.fullVideoSrc = toAbsoluteAssetPath(project.fullVideoSrc);
      }
    } else {
      // For "image" projects or other non-video types.
      // For non-video projects, dataset.src for grid is the main project source (no mobile variant for grid images here).
      post.dataset.src = toAbsoluteAssetPath(project.src);

      // Store poster data if available
      if (project.poster) {
        post.dataset.poster = toAbsoluteAssetPath(project.poster);
      }
      if (project.posterMobile) {
        post.dataset.posterMobile = toAbsoluteAssetPath(project.posterMobile);
      }

      // Additional images are stored in the images dataset for project detail view
    }

    // Store project images data for project detail view
    if (project.images && Array.isArray(project.images)) {
      post.dataset.images = JSON.stringify(project.images);
    } else {
      post.dataset.images = JSON.stringify([]);
    }

    // Count media items for hover dots (main src + additional images)
    let mediaCount = 1; // Always at least the main src
    if (project.images && Array.isArray(project.images)) {
      mediaCount += project.images.length;
    }
    const hoverDotsCount = mediaCount;

    // This array is only used to determine the *count* for hover dots on the grid item itself.
    // It does not need to store full paths or actual data, just needs the correct length.
    // const projectImagesForHoverDots = new Array(hoverDotsCount).fill(null); // Commented out as projectImagesForHoverDots is unused

    // --- Other dataset attributes for project details --- //
    if (project.fullVideoSrc)
      post.dataset.fullSrc = toAbsoluteAssetPath(project.fullVideoSrc); // Full version of video if different from grid thumbnail.
    if (project.poster)
      post.dataset.poster = toAbsoluteAssetPath(project.poster); // Main poster for videos.
    if (project.posterMobile)
      post.dataset.posterMobile = toAbsoluteAssetPath(project.posterMobile); // Mobile-specific main poster.
    post.dataset.software = project.software; // Software used, displayed as a string.
    post.dataset.description = project.description; // Main description text.
    post.dataset.mobileDescription =
      project.mobileDescription || project.description; // Mobile-specific description or fallback.
    post.dataset.bulletPoints = project.bulletPoints
      ? project.bulletPoints.join("|")
      : ""; // Array of bullet points, joined by pipe.
    post.dataset.toolsUsed = project.toolsUsed
      ? project.toolsUsed.join(", ")
      : ""; // Array of tools, joined by comma.
    post.dataset.idx = idx.toString(); // Index of the project, used for project navigation.
    post.dataset.workType = project.workType || "personal"; // Type of work (e.g., "personal", "client").

    // --- Create and append actual media element (img or video) for the grid display --- //
    if (project.type === "image") {
      const img = document.createElement("img");
      img.src = toAbsoluteAssetPath(project.src); // Grid images use the main `project.src`.
      img.alt = project.alt || project.title || "Project Image";
      post.appendChild(img);
    } else if (project.type === "video") {
      const video = document.createElement("video");
      // Grid videos use mobile-specific source if available, otherwise main source.
      video.src =
        mobileDevice && project.srcMobile
          ? toAbsoluteAssetPath(project.srcMobile)
          : toAbsoluteAssetPath(project.src);
      if (project.poster) video.poster = toAbsoluteAssetPath(project.poster); // Poster for the grid video.
      video.autoplay = true;
      video.muted = true;
      video.loop = true; // Standard attributes for silent autoplaying grid videos.
      video.setAttribute("playsinline", ""); // Important for iOS inline playback.
      video.setAttribute("disablePictureInPicture", ""); // Prevent PiP button.
      video.alt = project.alt || project.title || "Project Video";
      post.appendChild(video);
    }

    // --- Create text overlay elements for hover effects --- //
    const textContainer = createEl("div", "post-text-container"); // Main container for all hover text.

    // Work type label (e.g., "PERSONAL WORK", "CLIENT WORK").
    let projectWorkLabelText = "PERSONAL WORK";
    if (project.workType === "client") {
      projectWorkLabelText = "CLIENT WORK";
    }
    const projectWorkLabelEl = createEl(
      "div",
      "post-hover-project-work-label",
      projectWorkLabelText,
    );
    textContainer.appendChild(projectWorkLabelEl);

    // Project Title.
    const titleEl = createEl("div", "post-hover-title", project.title);

    // Wrapper for title (on desktop) and subtitle to group them for centering.
    const mainTextWrapper = createEl("div", "post-hover-text-main");

    // Title placement depends on screen size (CSS handles final positioning).
    if (window.matchMedia && window.matchMedia("(max-width: 767px)").matches) {
      textContainer.appendChild(mainTextWrapper); // Mobile: group title and subtitle
    }
    mainTextWrapper.appendChild(titleEl);
    if (project.subtitle && project.subtitle.trim() !== "") {
      const subtitleEl = createEl(
        "div",
        "post-hover-category-subtitle",
        project.subtitle,
      );
      mainTextWrapper.appendChild(subtitleEl);
    }
    if (!(window.matchMedia && window.matchMedia("(max-width: 767px)").matches)) {
      textContainer.appendChild(mainTextWrapper); // Desktop: group title and subtitle
    }

    // Image indicator dots, if there are multiple media items.
    if (hoverDotsCount > 1) {
      const dotsContainer = createEl("div", "post-hover-image-dots");
      for (let i = 0; i < hoverDotsCount; i++) {
        const dotEl = createEl("span", "post-hover-image-dot");
        dotsContainer.appendChild(dotEl);
      }
      textContainer.appendChild(dotsContainer);
    }

    post.appendChild(textContainer);

    return post;
  }

  // Clear existing feed content and populate with new project posts.
  feedContainer.innerHTML = "";
  // Always show 4 grid slots (2x2), fill with empty divs if needed
  const totalSlots = 4;
  const postsToShow = projects.slice(0, totalSlots);
  for (let i = 0; i < totalSlots; i++) {
    let post;
    if (i < postsToShow.length) {
      post = createPostElement(postsToShow[i], i);
    } else {
      post = document.createElement("div");
      post.className = "post empty-post";
    }
    feedContainer.appendChild(post);
  }
  // On mobile, add a real spacer div after the last post for bottom gap
  if (window.matchMedia && window.matchMedia("(max-width: 767px)").matches) {
    const spacer = document.createElement("div");
    spacer.style.width = "100%";
    spacer.style.height = "2px";
    spacer.style.flexShrink = "0";
    feedContainer.appendChild(spacer);
  }

  // After all posts are created, extract their dataset to form `allPostsData` for project detail view.
  const allPostsElements = Array.from(document.querySelectorAll(".post"));
  allPostsData = allPostsElements.map((p) => ({ ...p.dataset }));

  // Mobile preloading: preload mobile video sources if on mobile device
  if (isMobileDevice()) {
    console.log("Mobile device detected, starting mobile video preloading...");
    allPostsElements.forEach((post) => {
      if (post.dataset.type === "video" && post.dataset.srcMobile) {
        const video = document.createElement("video");
        video.src = post.dataset.srcMobile;
        video.preload = "metadata";
        video.muted = true;
        video.style.display = "none";
        document.body.appendChild(video);
      }
      if (post.dataset.fullVideoSrcMobile) {
        const video = document.createElement("video");
        video.src = post.dataset.fullVideoSrcMobile;
        video.preload = "metadata";
        video.muted = true;
        video.style.display = "none";
        document.body.appendChild(video);
      }
    });
  }

  // Posts are now display-only (no click handlers for detail view)

  // Add dimming effect for client work label hover
  const clientLabel = document.querySelector('.client-work-label-fixed');
  if (clientLabel) {
    clientLabel.addEventListener('mouseenter', () => {
      document.querySelectorAll('.post').forEach(post => {
        if (post.dataset.workType !== 'client') {
          post.classList.add('dimmed');
          post.classList.remove('hover');
        } else {
          post.classList.remove('dimmed');
          post.classList.add('hover');
        }
      });
    });
    clientLabel.addEventListener('mouseleave', () => {
      document.querySelectorAll('.post').forEach(post => {
        post.classList.remove('dimmed');
        post.classList.remove('hover');
      });
    });
  }

  // Add dimming effect for personal work label hover
  const personalLabel = document.querySelector('.personal-work-label-fixed');
  if (personalLabel) {
    personalLabel.addEventListener('mouseenter', () => {
      document.querySelectorAll('.post').forEach(post => {
        if (post.dataset.workType !== 'personal') {
          post.classList.add('dimmed');
          post.classList.remove('hover');
        } else {
          post.classList.remove('dimmed');
          post.classList.add('hover');
        }
      });
    });
    personalLabel.addEventListener('mouseleave', () => {
      document.querySelectorAll('.post').forEach(post => {
        post.classList.remove('dimmed');
        post.classList.remove('hover');
      });
    });
  }

  // ===== Masonry Layout, Video Playback & Intersection Observer ===== //
  const gridVideos = Array.from(
    document.querySelectorAll(".feed-container .video-post video"),
  ); // All video elements in the grid.
  let isMaximized = false; // Tracks if the parent window is maximized.
  let intersectionObserver = null; // Instance of IntersectionObserver for video visibility.

  // ===== Video State Management ===== //
  let videosPlayingBeforeDetailView = new Set(); // Tracks which videos were playing before detail view opened

  // ===== Ultra-Safe DOM Query Caching ===== //
  // Cache frequently accessed DOM elements to avoid repeated queries
  // This is 100% safe and only improves performance without changing behavior
  let cachedFeedContainer = null;
  let cachedFeedPosts = null;

  function getCachedDOMElements() {
    // Always get fresh DOM elements to ensure accurate calculations on resize
    cachedFeedContainer = document.querySelector(".feed-container");
    cachedFeedPosts = cachedFeedContainer ?
      Array.from(cachedFeedContainer.querySelectorAll(".post")) : [];
    return {
      feedContainer: cachedFeedContainer,
      posts: cachedFeedPosts
    };
  }



  // ===== Project Detail View State Management ===== //
  // Handle video state when switching between grid and detail views

  /**
   * Applies a full-width 2x2 masonry-style layout to the project posts in the feed container.
   * Calculates column widths to fill the available width and positions posts absolutely in two columns.
   */
  function applyMasonryLayout() {
    const { feedContainer: currentFeedContainer, posts: currentFeedPosts } = getCachedDOMElements();
    if (!currentFeedContainer) {
      sendMessageToParent({ type: "projects-ready" });
      return;
    }
    if (currentFeedPosts.length === 0) {
      if (!currentFeedContainer.classList.contains("loaded")) {
        currentFeedContainer.classList.add("loaded");
      }
      currentFeedContainer.style.height = "auto";
      sendMessageToParent({ type: "projects-ready" });
      return;
    }
    // Set container height to fill iframe
    const containerHeight = currentFeedContainer.parentElement.offsetHeight;
    const containerWidth = currentFeedContainer.parentElement.offsetWidth;
    currentFeedContainer.style.height = `${containerHeight}px`;
    // Detect mobile for 1-column layout
    const isMobile = window.matchMedia && window.matchMedia("(max-width: 767px)").matches;
    let numColumns = isMobile ? 1 : 2;
    let numRows = isMobile ? currentFeedPosts.length : 2;
    const gap = 12;
    const horizontalGap = 16;
    const containerStyle = getComputedStyle(currentFeedContainer);
    const containerPaddingLeft = parseFloat(containerStyle.paddingLeft) || 0;
    const containerPaddingTop = parseFloat(containerStyle.paddingTop) || 0;
    const containerPaddingRight = parseFloat(containerStyle.paddingRight) || 0;
    const containerPaddingBottom = parseFloat(containerStyle.paddingBottom) || 0;
    // Height-limited cell size
    const maxCellHeightByHeight = Math.floor((containerHeight - containerPaddingTop - containerPaddingBottom - gap) / numRows);
    const maxCellWidthByHeight = Math.floor(maxCellHeightByHeight * 1.25);
    // Width-limited cell size
    const maxCellWidthByWidth = Math.floor((containerWidth - containerPaddingLeft - containerPaddingRight - horizontalGap) / numColumns);
    const maxCellHeightByWidth = Math.floor(maxCellWidthByWidth / 1.25);
    // Choose the limiting factor
    let cellWidth, cellHeight;
    if (maxCellWidthByHeight * numColumns + horizontalGap <= containerWidth - containerPaddingLeft - containerPaddingRight) {
      // Height is limiting
      cellHeight = maxCellHeightByHeight;
      cellWidth = maxCellWidthByHeight;
    } else {
      // Width is limiting
      cellWidth = maxCellWidthByWidth;
      cellHeight = maxCellHeightByWidth;
    }
    // Set feed-container width and height to fit grid
    const gridWidth = Math.floor(numColumns * cellWidth + (numColumns - 1) * horizontalGap + containerPaddingLeft + containerPaddingRight);
    const gridHeight = Math.floor(numRows * cellHeight + (numRows - 1) * gap + containerPaddingTop + containerPaddingBottom);
    if (isMobile) {
      currentFeedContainer.style.width = '100%';
      currentFeedContainer.style.height = `${gridHeight + 16}px`;
      currentFeedContainer.style.marginTop = '0px';
    } else {
      currentFeedContainer.style.width = `${gridWidth}px`;
      currentFeedContainer.style.height = `${gridHeight}px`;
      // Vertically center grid if there is excess space
      if (gridHeight < containerHeight) {
        currentFeedContainer.style.marginTop = `${Math.floor((containerHeight - gridHeight) / 2)}px`;
      } else {
        currentFeedContainer.style.marginTop = '0px';
      }
    }
    currentFeedContainer.style.left = "";
    currentFeedContainer.style.top = "";
    currentFeedContainer.style.position = "";
    // Position each post
    currentFeedPosts.forEach((post, i) => {
      if (isMobile) {
        post.style.position = "static";
        post.style.width = "95%";
        post.style.left = "";
        post.style.top = "";
        post.style.height = "";
        post.style.margin = "0 auto 16px auto";
      } else {
        post.style.position = "absolute";
        post.style.width = `${cellWidth}px`;
        post.style.left = `${Math.floor(containerPaddingLeft + (i % numColumns) * (cellWidth + horizontalGap))}px`;
        post.style.height = "";
        const row = Math.floor(i / numColumns);
        post.style.top = `${Math.floor(containerPaddingTop + row * (cellHeight + gap))}px`;
        post.style.margin = "0";
      }
    });
    if (!currentFeedContainer.classList.contains("loaded")) {
      currentFeedContainer.classList.add("loaded");
    }
    sendMessageToParent({ type: "projects-ready" });
  }

  /**
   * Initializes masonry layout after ensuring all grid videos have loaded their metadata
   * (or at least attempted to load), so their dimensions are somewhat stable for layout calculation.
   */
  function initMasonryWithVideoCheck() {
    if (!feedContainer) return;

    const videos = gridVideos;

    // If no videos, apply masonry layout directly.
    if (videos.length === 0) {
      applyMasonryLayout();
      return;
    }

    let videosToMonitor = videos.length;
    let videosReported = 0; // Count of videos that have either loaded metadata or errored.

    // Callback when a video has loaded metadata or errored.
    const onMediaReady = (mediaElement) => {
      // Clean up listeners for this specific media element.
      mediaElement.removeEventListener("loadedmetadata", onMediaReadyHandler);
      mediaElement.removeEventListener("loadeddata", onMediaReadyHandler);
      mediaElement.removeEventListener("error", onErrorHandler);
      videosReported++;
      // If all videos have reported, apply masonry layout.
      if (videosReported === videosToMonitor) {
        // Mark that we're waiting for recalculation to prevent showing the grid too early
        const { feedContainer: currentFeedContainer } = getCachedDOMElements();
        if (currentFeedContainer) {
          currentFeedContainer.dataset.waitingForRecalc = "true";
        }

        applyMasonryLayout();
        // Recalculate layout after a short delay to account for video dimension changes
        setTimeout(() => {
          applyMasonryLayout();
          // Now it's safe to show the grid
          if (currentFeedContainer) {
            delete currentFeedContainer.dataset.waitingForRecalc;
            if (!currentFeedContainer.dataset.revealProcessStarted) {
              currentFeedContainer.dataset.revealProcessStarted = "true";
              if (!currentFeedContainer.classList.contains("loaded")) {
                currentFeedContainer.classList.add("loaded");
              }
              sendMessageToParent({ type: "projects-ready" });
            }
          }
        }, 150);
      }
    };

    // Event handler functions to be bound to video elements.
    const onMediaReadyHandler = function (event) {
      onMediaReady(this, event.type);
    };
    const onErrorHandler = function (event) {
      // Treat error as 'ready' for layout purposes to avoid blocking indefinitely.
      onMediaReady(this, event.type);
    };

    videos.forEach((video) => {
      if (video.readyState >= 2) {
        // HAVE_CURRENT_DATA or more: metadata is likely available.
        videosReported++;
      } else {
        video.addEventListener("loadedmetadata", onMediaReadyHandler);
        video.addEventListener("loadeddata", onMediaReadyHandler); // loadeddata as a stronger guarantee
        video.addEventListener("error", onErrorHandler);
        // Also listen for resize events in case dimensions change after metadata loads
        video.addEventListener("resize", onMediaReadyHandler);
      }
    });

    // If all videos were already ready (e.g., cached), apply layout immediately.
    if (videosReported === videosToMonitor && videosToMonitor > 0) {
      // Mark that we're waiting for recalculation to prevent showing the grid too early
      const currentFeedContainer = document.querySelector(".feed-container");
      if (currentFeedContainer) {
        currentFeedContainer.dataset.waitingForRecalc = "true";
      }

      applyMasonryLayout();
      // Recalculate layout after a short delay to account for video dimension changes
      setTimeout(() => {
        applyMasonryLayout();
        // Now it's safe to show the grid
        const { feedContainer: currentFeedContainer } = getCachedDOMElements();
        if (currentFeedContainer) {
          delete currentFeedContainer.dataset.waitingForRecalc;
          if (!currentFeedContainer.dataset.revealProcessStarted) {
            currentFeedContainer.dataset.revealProcessStarted = "true";
            if (!currentFeedContainer.classList.contains("loaded")) {
              currentFeedContainer.classList.add("loaded");
            }
            sendMessageToParent({ type: "projects-ready" });
          }
        }
      }, 150);
    }
  }

  // Initial setup for masonry layout.
  if (feedContainer) {
    initMasonryWithVideoCheck();
    let resizeTimeout;
    // Re-apply masonry on window resize, with a short timeout to avoid excessive calls.
    window.addEventListener("resize", () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(applyMasonryLayout, 150);
    });
  }

  /**
   * Saves the current playing state of all grid videos before detail view opens.
   * Stores references to videos that are currently playing in videosPlayingBeforeDetailView Set.
   */
  function saveVideoPlayingStates() {
    videosPlayingBeforeDetailView.clear();
    gridVideos.forEach((video) => {
      if (!video.paused) {
        videosPlayingBeforeDetailView.add(video);
      }
    });
  }

  /**
   * Restores the playing state of videos that were playing before detail view opened.
   * Only resumes videos that were actually playing before, not all visible videos.
   */
  function restoreVideoPlayingStates() {
    videosPlayingBeforeDetailView.forEach((video) => {
      // Double-check the video element still exists and is paused
      if (video && video.paused) {
        video.play().catch((error) => {
          console.error(`Failed to restore video playback for ${video.src}:`, error);
        });
      }
    });
    // Clear the saved state after restoring
    videosPlayingBeforeDetailView.clear();
  }

  /**
   * Plays or pauses grid videos based on their visibility status (`__isIntersecting` flag).
   */
  function playVisibleVideos() {
    if (!intersectionObserver) return;
    gridVideos.forEach((video) => {
      if (video.__isIntersecting) {
        // Flag set by IntersectionObserver.
        if (video.paused) video.play().catch(() => {}); // Play if visible and paused.
      } else {
        if (!video.paused) video.pause(); // Pause if not visible and playing.
      }
    });
  }

  /**
   * Sets up the IntersectionObserver to monitor the visibility of grid videos.
   * Updates a custom `__isIntersecting` flag on each video element.
   */
  function setupIntersectionObserver() {
    if (intersectionObserver) intersectionObserver.disconnect(); // Clean up existing observer.
    intersectionObserver = new window.IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          entry.target.__isIntersecting = entry.isIntersecting; // Set visibility flag.
        });
        playVisibleVideos(); // Update playback based on new visibility states.
      },
      { root: document.querySelector(".scroll-content"), threshold: 0.1 }, // Observe within scroll container, 10% visibility threshold.
    );
    gridVideos.forEach((video) => {
      intersectionObserver.observe(video); // Start observing each grid video.
    });
    requestAnimationFrame(playVisibleVideos); // Initial check for visible videos.
  }

  /**
   * Disconnects the IntersectionObserver and cleans up custom flags on video elements.
   */
  function cleanupIntersectionObserver() {
    if (intersectionObserver) {
      intersectionObserver.disconnect();
      intersectionObserver = null;
    }
    gridVideos.forEach((video) => delete video.__isIntersecting); // Remove custom flag.
  }

  /**
   * Manages video playback and CSS classes based on the window's maximized state.
   * @param {boolean} maximized - True if the window is maximized, false otherwise.
   */
  function setMaximizedState(maximized) {
    isMaximized = maximized;
    const bodyEl = document.body;
    const maximizedClassName = "projects-window-maximized"; // CSS class for body when window is maximized.

    if (maximized) bodyEl.classList.add(maximizedClassName);
    else bodyEl.classList.remove(maximizedClassName);

    // When window is maximized, play all grid videos and disable intersection observer.
    // When unmaximized, pause all grid videos and re-enable intersection observer for visibility-based playback.
    if (isMaximized) {
      if (typeof cleanupIntersectionObserver === "function")
        cleanupIntersectionObserver();
      gridVideos.forEach((video) => video.play().catch(() => {})); // Play all, catch errors if any video can't play.
    } else {
      gridVideos.forEach((video) => video.pause());
      if (typeof setupIntersectionObserver === "function")
        setupIntersectionObserver();
    }
  }

  setupIntersectionObserver(); // Initial setup of the observer.

  // ----- General iFrame Interaction Click Listener ----- //
  // Notifies the parent window of any click within this iframe for focus management or other shell behaviors.
  document.addEventListener("click", () => {
    if (window.parent && window.parent !== window) {
      window.parent.postMessage({ type: "iframe-interaction" }, "*");
    }
  });

  // ===== Mobile Feed Interaction (Scroll-based Overlay) ===== //
  // Shows a subtle hover effect on project posts when the user scrolls on mobile devices.
  const scrollContentElement = document.querySelector(".scroll-content");
  const feedContainerElement = document.querySelector(".feed-container");
  let mobileInteractionTimeout = null; // Timeout ID for clearing the hover effect.

  if (scrollContentElement && feedContainerElement) {
    const isMobileMediaQuery = window.matchMedia("(max-width: 767px)");

    // Handles scroll events to trigger the temporary hover effect on mobile.
    const handleScroll = () => {
      if (!isMobileMediaQuery.matches) return; // Only apply on mobile.
      if (mobileInteractionTimeout) {
        clearTimeout(mobileInteractionTimeout);
        mobileInteractionTimeout = null;
      }
      // Add 'hover' class to all posts to show overlays.
      document
        .querySelectorAll(".post")
        .forEach((post) => post.classList.add("hover"));
      // Remove 'hover' class after a short duration.
      mobileInteractionTimeout = setTimeout(() => {
        document
          .querySelectorAll(".post")
          .forEach((post) => post.classList.remove("hover"));
        mobileInteractionTimeout = null;
      }, 750); // Duration of the hover effect.
    };

    scrollContentElement.addEventListener("scroll", handleScroll, {
      passive: true,
    });
  }
  // --- End Mobile Feed Interaction ---

  // --- Aggressive Pinch Zoom Prevention --- //
  // Prevents default browser pinch-zoom and other touch gestures.
  /**
   * Determines if zoom/gesture prevention should be applied based on the event target.
   * Allows gestures if the target is within the project detail view.
   * @param {Event} event - The gesture or touch event.
   * @returns {boolean} True if prevention should apply, false otherwise.
   */
  function shouldPreventZoom(event) {
    const target = event.target;
    // Check if we're in project detail view and allow gestures there
    if (target.closest("#project-detail-view")) {
      return false; // Do not prevent if inside project detail view
    }
    return true; // Prevent by default for grid view
  }

  // Event listeners for preventing various zoom/gesture behaviors.
  document.addEventListener(
    "gesturestart",
    (e) => {
      if (shouldPreventZoom(e)) e.preventDefault();
    },
    { passive: false },
  );
  document.addEventListener(
    "gesturechange",
    (e) => {
      if (shouldPreventZoom(e)) e.preventDefault();
    },
    { passive: false },
  );
  document.addEventListener(
    "gestureend",
    (e) => {
      if (shouldPreventZoom(e)) e.preventDefault();
    },
    { passive: false },
  );
  document.addEventListener(
    "touchstart",
    (e) => {
      if (e.touches.length > 1 && shouldPreventZoom(e)) e.preventDefault();
    },
    { passive: false },
  ); // Multi-touch prevention.
  document.addEventListener(
    "touchmove",
    (e) => {
      if (e.touches.length > 1 && shouldPreventZoom(e)) e.preventDefault();
    },
    { passive: false },
  );

  let lastTouchEnd = 0; // For double-tap prevention.
  document.addEventListener(
    "touchend",
    (event) => {
      if (!shouldPreventZoom(event)) return;
      const now = new Date().getTime();
      if (now - lastTouchEnd <= 300) event.preventDefault(); // Prevent action if taps are close (double-tap).
      lastTouchEnd = now;
    },
    { passive: false },
  );

  document.addEventListener(
    "wheel",
    (event) => {
      if (!shouldPreventZoom(event)) return;
      // Prevent Ctrl+wheel zoom and general wheel events on body/documentElement to avoid page-level zoom.
      if (
        event.ctrlKey ||
        event.target === document.body ||
        event.target === document.documentElement
      ) {
        event.preventDefault();
      }
    },
    { passive: false },
  );
  // --- End Aggressive Pinch Zoom Prevention ---

  // ===== Global Message Listener (from Parent) ===== //
  // Handles messages from the parent window (e.g., toolbar actions).
  window.addEventListener("message", (event) => {
    if (!event.data || typeof event.data.type !== "string") return; // Ignore malformed messages.

    const { type, action, maximized } = event.data;

    // Handle messages to set the maximized state of the window (from parent shell).
    if (type === "set-maximized-state") {
      setMaximizedState(maximized);
    }
  });
}); // Closes DOMContentLoaded
