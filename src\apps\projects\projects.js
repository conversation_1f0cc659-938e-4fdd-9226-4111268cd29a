/*
 * projects.js — Clean, optimized Projects App functionality
 * Modern ES6+ with efficient DOM manipulation and responsive design
 */

// ===== IMPORTS ===== //
import { isMobileDevice } from "../../scripts/utils/device.js";

// ===== CONSTANTS ===== //
const BREAKPOINT_MOBILE = 1200;
const SELECTORS = {
  grid: '#projects-grid-view',
  detailPage: '#project-detail-page',
  detailContainer: '.detail-container',
  detailContent: '.detail-content',
  scrollIndicator: '.scroll-indicator',
  scrollDot: '.scroll-dot',
  workLabels: '.work-label'
};

const CLASSES = {
  loaded: 'loaded',
  visible: 'visible',
  hidden: 'hidden',
  hover: 'hover',
  dimmed: 'dimmed',
  empty: 'empty'
};

// ===== STATE ===== //
let projectsData = [];
let intersectionObserver = null;
let isMaximized = false;

// ===== UTILITY FUNCTIONS ===== //
const createElement = (tag, className = '', content = '') => {
  const element = document.createElement(tag);
  if (className) element.className = className;
  if (content) element.textContent = content;
  return element;
};

const toAbsolutePath = (path) => {
  if (!path || path.startsWith('http') || path.startsWith('../')) return path;
  return `../../../${path}`;
};

const sendMessageToParent = (payload) => {
  if (window.parent && window.parent !== window) {
    window.parent.postMessage(payload, "*");
  }
};

const isMobile = () => window.innerWidth < BREAKPOINT_MOBILE;

// ===== PROJECT CREATION ===== //
const createProject = (project, index) => {
  const projectEl = createElement('div', `project ${project.type}-project`);
  
  // Set data attributes
  Object.assign(projectEl.dataset, {
    type: project.type,
    title: project.title,
    subtitle: project.subtitle || '',
    description: project.description || '',
    workType: project.workType || 'personal',
    idx: index.toString()
  });

  // Create media element
  const mediaEl = project.type === 'video' 
    ? createVideoElement(project)
    : createImageElement(project);
  
  projectEl.appendChild(mediaEl);

  // Create text overlay
  const textEl = createTextOverlay(project);
  projectEl.appendChild(textEl);

  return projectEl;
};

const createVideoElement = (project) => {
  const video = createElement('video');
  const isMobileDevice = isMobile();
  
  video.src = isMobileDevice && project.srcMobile 
    ? toAbsolutePath(project.srcMobile)
    : toAbsolutePath(project.src);
    
  if (project.poster) video.poster = toAbsolutePath(project.poster);
  
  Object.assign(video, {
    autoplay: true,
    muted: true,
    loop: true,
    playsInline: true
  });
  
  video.setAttribute('disablePictureInPicture', '');
  video.alt = project.alt || project.title || 'Project Video';
  
  return video;
};

const createImageElement = (project) => {
  const img = createElement('img');
  img.src = toAbsolutePath(project.src);
  img.alt = project.alt || project.title || 'Project Image';
  return img;
};

const createTextOverlay = (project) => {
  const textContainer = createElement('div', 'project-text');
  
  // Work type label
  const workLabel = createElement('div', 'project-work-label', 
    project.workType === 'client' ? 'CLIENT WORK' : 'PERSONAL WORK');
  textContainer.appendChild(workLabel);

  // Main text wrapper
  const mainText = createElement('div', 'project-main-text');
  
  const title = createElement('div', 'project-title', project.title);
  mainText.appendChild(title);
  
  if (project.subtitle?.trim()) {
    const subtitle = createElement('div', 'project-subtitle', project.subtitle);
    mainText.appendChild(subtitle);
  }
  
  textContainer.appendChild(mainText);

  // Media dots if multiple items
  const mediaCount = 1 + (project.images?.length || 0);
  if (mediaCount > 1) {
    const dotsContainer = createElement('div', 'project-dots');
    for (let i = 0; i < mediaCount; i++) {
      dotsContainer.appendChild(createElement('span', 'project-dot'));
    }
    textContainer.appendChild(dotsContainer);
  }

  return textContainer;
};

// ===== PROJECT DETAIL PAGE ===== //
const showProjectDetail = (projectIndex) => {
  const project = projectsData[projectIndex];
  if (!project) return;

  const detailPage = document.querySelector(SELECTORS.detailPage);
  const detailContainer = document.querySelector(SELECTORS.detailContainer);
  
  if (!detailPage || !detailContainer) return;

  // Populate content
  detailContainer.innerHTML = `
    <h1 style="color: white; font-size: 2rem; margin-bottom: 20px;">${project.title}</h1>
    <p style="color: white; font-size: 1.2rem;">Project details will go here...</p>
    <button onclick="hideProjectDetail()" style="margin-top: 20px; padding: 10px 20px; background: white; border: none; cursor: pointer;">Back to Grid</button>
  `;

  // Show with transition
  detailPage.style.display = 'block';
  requestAnimationFrame(() => {
    detailPage.classList.add(CLASSES.visible);
  });

  setupDetailScrollIndicator();
};

const hideProjectDetail = () => {
  const detailPage = document.querySelector(SELECTORS.detailPage);
  if (!detailPage) return;

  detailPage.classList.remove(CLASSES.visible);
  setTimeout(() => {
    detailPage.style.display = 'none';
  }, 300);
};

// Make globally available
window.hideProjectDetail = hideProjectDetail;

// ===== SCROLL INDICATOR ===== //
const setupDetailScrollIndicator = () => {
  const indicator = document.querySelector(`${SELECTORS.detailPage} ${SELECTORS.scrollIndicator}`);
  const dot = indicator?.querySelector(SELECTORS.scrollDot);
  const content = document.querySelector(SELECTORS.detailContent);

  if (!indicator || !dot || !content) return;

  const updateIndicator = () => {
    const { scrollTop, scrollHeight, clientHeight } = content;
    const maxScroll = scrollHeight - clientHeight;
    
    if (maxScroll > 0) {
      const percentage = scrollTop / maxScroll;
      const maxDotPosition = indicator.offsetWidth - dot.offsetWidth;
      dot.style.left = `${percentage * maxDotPosition}px`;
      indicator.style.opacity = '1';
    } else {
      dot.style.left = '0px';
      indicator.style.opacity = '1';
    }
  };

  content.addEventListener('scroll', updateIndicator, { passive: true });
  window.addEventListener('resize', updateIndicator);
  
  indicator.style.opacity = '1';
  setTimeout(updateIndicator, 100);
};

// ===== VIDEO MANAGEMENT ===== //
const setupVideoObserver = () => {
  if (intersectionObserver) intersectionObserver.disconnect();

  const videos = document.querySelectorAll('.project video');
  if (videos.length === 0) return;

  intersectionObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        const video = entry.target;
        if (entry.isIntersecting) {
          if (video.paused) video.play().catch(() => {});
        } else {
          if (!video.paused) video.pause();
        }
      });
    },
    { root: document.querySelector(SELECTORS.grid), threshold: 0.1 }
  );

  videos.forEach(video => intersectionObserver.observe(video));
};

const setMaximizedState = (maximized) => {
  isMaximized = maximized;
  const videos = document.querySelectorAll('.project video');
  
  if (maximized) {
    if (intersectionObserver) {
      intersectionObserver.disconnect();
      intersectionObserver = null;
    }
    videos.forEach(video => video.play().catch(() => {}));
  } else {
    videos.forEach(video => video.pause());
    setupVideoObserver();
  }
};

// ===== EVENT HANDLERS ===== //
const handleProjectClick = (event) => {
  if (event.target.tagName === 'A') return;
  
  const project = event.currentTarget;
  const projectIndex = parseInt(project.dataset.idx, 10);
  
  if (!isNaN(projectIndex)) {
    showProjectDetail(projectIndex);
  }
};

const setupWorkLabelHover = () => {
  const labels = document.querySelectorAll(SELECTORS.workLabels);
  const projects = document.querySelectorAll('.project');

  labels.forEach(label => {
    const workType = label.classList.contains('work-label--left') ? 'client' : 'personal';

    label.addEventListener('mouseenter', () => {
      projects.forEach(project => {
        if (project.dataset.workType !== workType) {
          project.classList.add(CLASSES.dimmed);
        } else {
          project.classList.add(CLASSES.hover);
        }
      });
    });

    label.addEventListener('mouseleave', () => {
      projects.forEach(project => {
        project.classList.remove(CLASSES.dimmed, CLASSES.hover);
      });
    });
  });
};

const setupMobileInteraction = () => {
  if (!isMobile()) return;

  let mobileTimeout = null;

  const handleScroll = () => {
    if (mobileTimeout) clearTimeout(mobileTimeout);

    // Show hover state on all projects
    document.querySelectorAll('.project').forEach(project => {
      project.classList.add(CLASSES.hover);
    });

    // Hide after delay
    mobileTimeout = setTimeout(() => {
      document.querySelectorAll('.project').forEach(project => {
        project.classList.remove(CLASSES.hover);
      });
    }, 750);
  };

  document.querySelector(SELECTORS.grid).addEventListener('scroll', handleScroll, { passive: true });
};

// ===== MAIN INITIALIZATION ===== //
const init = async () => {
  try {
    // Load projects data
    const response = await fetch("../../../projects.json");
    projectsData = await response.json();

    const grid = document.querySelector(SELECTORS.grid);
    if (!grid) throw new Error('Projects grid not found');

    // Create projects (limit to 4 for 2x2 grid)
    const projectsToShow = projectsData.slice(0, 4);
    const totalSlots = 4;

    for (let i = 0; i < totalSlots; i++) {
      let projectEl;

      if (i < projectsToShow.length) {
        projectEl = createProject(projectsToShow[i], i);
        projectEl.addEventListener('click', handleProjectClick);
      } else {
        projectEl = createElement('div', `project ${CLASSES.empty}`);
      }

      grid.appendChild(projectEl);
    }

    // Setup interactions
    setupWorkLabelHover();
    setupMobileInteraction();
    setupVideoObserver();

    // Show grid
    grid.classList.add(CLASSES.loaded);

    // Notify parent
    sendMessageToParent({ type: "projects-ready" });

  } catch (error) {
    console.error("Failed to initialize projects:", error);
  }
};

// ===== MESSAGE HANDLING ===== //
const handleMessage = (event) => {
  if (!event.data?.type) return;

  const { type, maximized } = event.data;

  if (type === "set-maximized-state") {
    setMaximizedState(maximized);
  }
};

// ===== EVENT LISTENERS ===== //
document.addEventListener("DOMContentLoaded", init);
window.addEventListener("message", handleMessage);

// Notify parent of iframe interactions
document.addEventListener("click", () => {
  sendMessageToParent({ type: "iframe-interaction" });
});

// Prevent zoom gestures
const preventZoom = (event) => {
  if (event.touches?.length > 1 || event.ctrlKey) {
    event.preventDefault();
  }
};

document.addEventListener("touchstart", preventZoom, { passive: false });
document.addEventListener("touchmove", preventZoom, { passive: false });
document.addEventListener("wheel", preventZoom, { passive: false });
