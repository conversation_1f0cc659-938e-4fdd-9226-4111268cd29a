/*
 * projects.css — Clean, optimized styles for Projects App
 * Modern flexbox/grid layout with responsive design
 */

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  --breakpoint-mobile: 1200px;
  --container-padding: 1rem;
  --grid-gap: 1rem;
  --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-medium: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --color-bg-primary: linear-gradient(to bottom, #0a0f1a, #0b1a24 60%, #0a1a1f 100%);
  --color-bg-fallback: #0a0f1a;
  --color-text-primary: #ffffff;
  --color-text-secondary: rgba(255, 255, 255, 0.8);
  --color-overlay: rgba(0, 0, 0, 0.75);
  --color-border: rgba(255, 255, 255, 0.1);
  --color-shadow: rgba(0, 0, 0, 0.45);
  --font-primary: 'Wix Madefor Display', Arial, sans-serif;
  --font-secondary: 'Work Sans', Arial, sans-serif;
  --z-aurora: 1;
  --z-content: 10;
  --z-overlay: 20;
  --z-modal: 2000;
}

/* ===== BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  height: 100%;
  background: var(--color-bg-primary);
  background-color: var(--color-bg-fallback);
  line-height: 1.6;
  overflow: hidden;
}

body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: var(--font-primary);
  color: var(--color-text-primary);
  background: transparent;
  overflow-x: hidden;
  touch-action: none;
}

/* ===== SCROLLBAR HIDING ===== */
.projects-grid-view,
.detail-content {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.projects-grid-view::-webkit-scrollbar,
.detail-content::-webkit-scrollbar {
  display: none;
}

/* ===== DEBUG BORDERS ===== */
.projects-grid {
  border: 3px solid red !important;
}

.project {
  border: 2px solid lime !important;
}

.project-text {
  border: 1px solid orange !important;
}

.work-labels {
  border: 2px solid purple !important;
}

.project-detail-page {
  border: 3px solid cyan !important;
}

.detail-container {
  border: 2px solid yellow !important;
}

.scroll-indicator {
  border: 2px solid magenta !important;
}

/* ===== MAIN LAYOUT ===== */
.projects-grid {
  height: 100vh;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: auto auto;
  gap: var(--grid-gap);
  padding: var(--container-padding);
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: var(--z-content);
  overflow-y: auto;
  opacity: 0;
  transition: opacity var(--transition-medium);
  box-sizing: border-box;
  align-content: center;
  justify-content: center;
}

.projects-grid.loaded {
  opacity: 1;
}

.projects-grid.hidden {
  opacity: 0;
  pointer-events: none;
}

/* ===== PROJECT POSTS ===== */
.project {
  position: relative;
  width: 100%;
  aspect-ratio: 1350 / 1080;
  min-height: 200px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  box-shadow: 0 2px 12px var(--color-shadow);
  transition:
    transform var(--transition-fast),
    box-shadow var(--transition-fast),
    border-color var(--transition-fast);
}

.project.empty {
  background: none;
  box-shadow: none;
  border: none;
  pointer-events: none;
  cursor: default;
}

.project.dimmed {
  opacity: 0.3;
  filter: grayscale(1);
  transition: opacity var(--transition-fast), filter var(--transition-fast);
}

.project img,
.project video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.project video {
  min-width: 100%;
}

/* Project overlay */
.project::after {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--color-overlay);
  opacity: 0;
  transition: opacity var(--transition-fast);
  pointer-events: none;
  z-index: 1;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: inherit;
}

/* Project text overlay */
.project-text {
  position: absolute;
  inset: 0;
  padding: 8% 6%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 0.7em;
  text-align: center;
  opacity: 0;
  transition: opacity var(--transition-fast);
  pointer-events: none;
  z-index: 2;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-radius: inherit;
}

.project-work-label {
  font-family: var(--font-secondary);
  font-size: clamp(0.7rem, 2vw, 0.9rem);
  font-weight: 700;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: var(--color-text-secondary);
}

.project-title {
  font-size: clamp(1.1rem, 3vw, 1.4rem);
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-text-primary);
}

.project-subtitle {
  font-size: clamp(0.9rem, 2.5vw, 1.1rem);
  font-weight: 400;
  color: var(--color-text-secondary);
}

.project-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: clamp(2px, 0.5vw, 4px);
}

.project-dot {
  width: clamp(6px, 1.5vw, 10px);
  height: clamp(6px, 1.5vw, 10px);
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.6);
}

/* ===== HOVER EFFECTS ===== */
@media (hover: hover) and (pointer: fine) {
  .project:hover {
    transform: scale(1.03);
    z-index: var(--z-overlay);
    box-shadow: 0 6px 24px var(--color-shadow);
    border-color: var(--color-text-primary);
  }

  .project:hover::after,
  .project:hover .project-text {
    opacity: 1;
  }
}

.project.hover::after,
.project.hover .project-text {
  opacity: 1;
}

.project.hover {
  border-color: var(--color-text-primary);
  z-index: var(--z-overlay);
}

/* ===== WORK TYPE LABELS ===== */
.work-labels {
  position: fixed;
  inset: 0;
  pointer-events: none;
  z-index: var(--z-content);
}

.work-label {
  position: absolute;
  top: 50%;
  transform: translateY(-50%) rotate(-90deg);
  font-family: var(--font-secondary);
  font-size: 0.9rem;
  font-weight: 700;
  letter-spacing: 0.2em;
  text-transform: uppercase;
  color: var(--color-text-secondary);
  cursor: pointer;
  pointer-events: auto;
  transition: color var(--transition-fast);
}

.work-label--left {
  left: 1rem;
  transform-origin: center;
}

.work-label--right {
  right: 1rem;
  transform-origin: center;
}

.work-label:hover {
  color: var(--color-text-primary);
}

/* ===== AURORA BACKGROUND ===== */
.aurora {
  position: absolute;
  inset: 0;
  z-index: var(--z-aurora);
  pointer-events: none;
  overflow: hidden;
  filter: blur(32px);
}

.aurora-blob {
  position: absolute;
  border-radius: 50%;
  opacity: 0.22;
  filter: blur(48px);
  mix-blend-mode: lighten;
  will-change: transform, opacity;
  animation-timing-function: ease-in-out;
}

.blob1 {
  width: 60vw;
  height: 40vw;
  left: 5vw;
  top: -30vh;
  background: radial-gradient(circle, transparent 0%, #00ffd0 40%, #00bfff 80%, transparent 100%);
  animation: aurora-move1 18s infinite alternate;
}

.blob2 {
  width: 50vw;
  height: 30vw;
  left: 55vw;
  top: 35vh;
  background: radial-gradient(circle, transparent 0%, #00e0ff 35%, #0077ff 80%, transparent 100%);
  animation: aurora-move2 22s infinite alternate;
}

.blob3 {
  width: 40vw;
  height: 25vw;
  left: 10vw;
  top: 60vh;
  background: radial-gradient(circle, transparent 0%, #00ffb3 30%, #00ffd0 70%, transparent 100%);
  opacity: 0.18;
  animation: aurora-move3 26s infinite alternate;
}

.blob4 {
  width: 35vw;
  height: 20vw;
  left: 70vw;
  top: 10vh;
  background: radial-gradient(circle, transparent 0%, #0099ff 25%, #00ccff 75%, transparent 100%);
  opacity: 0.15;
  animation: aurora-move4 30s infinite alternate;
}

.blob5 {
  width: 45vw;
  height: 35vw;
  left: 25vw;
  top: 80vh;
  background: radial-gradient(circle, transparent 0%, #00ffcc 20%, #00e6ff 60%, transparent 100%);
  opacity: 0.2;
  animation: aurora-move5 24s infinite alternate;
}

@keyframes aurora-move1 {
  0% { transform: translateY(-20vh) scale(0.9); }
  50% { transform: translateY(30vh) scale(1.1); }
  100% { transform: translateY(70vh) scale(0.95); }
}

@keyframes aurora-move2 {
  0% { transform: translateY(-10vh) scale(1.05); }
  50% { transform: translateY(25vh) scale(0.9); }
  100% { transform: translateY(60vh) scale(1.1); }
}

@keyframes aurora-move3 {
  0% { transform: translateY(-15vh) scale(1.1); }
  50% { transform: translateY(40vh) scale(0.85); }
  100% { transform: translateY(85vh) scale(1.05); }
}

@keyframes aurora-move4 {
  0% { transform: translateY(10vh) scale(0.95); }
  50% { transform: translateY(50vh) scale(1.15); }
  100% { transform: translateY(90vh) scale(0.9); }
}

@keyframes aurora-move5 {
  0% { transform: translateY(-25vh) scale(1.0); }
  50% { transform: translateY(40vh) scale(1.1); }
  100% { transform: translateY(80vh) scale(0.95); }
}

/* ===== PROJECT DETAIL PAGE ===== */
.project-detail-page {
  position: fixed;
  inset: 0;
  background: var(--color-bg-fallback);
  z-index: var(--z-modal);
  opacity: 0;
  transition: opacity var(--transition-medium);
  display: none;
}

.project-detail-page.visible {
  opacity: 1;
}

.detail-content {
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
}

.detail-container {
  min-height: 100%;
  padding: 2.5rem 1.25rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* ===== SCROLL INDICATOR ===== */
.scroll-indicator {
  position: absolute;
  top: 10px;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--color-border);
  z-index: calc(var(--z-modal) + 100);
  pointer-events: none;
}

.scroll-dot {
  position: absolute;
  top: -2px;
  left: 0;
  width: 8px;
  height: 8px;
  background: var(--color-text-primary);
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  transition: left 0.1s ease-out;
}
