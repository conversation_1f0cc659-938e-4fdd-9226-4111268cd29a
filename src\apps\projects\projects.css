/*
 * projects.css — Styles for Projects App in Windows XP Simulation
 * Styles the My Projects window, feed, and responsive layout.
 * @file src/apps/projects/projects.css
 */

/* ===== DEBUG BORDERS ===== */
/* Grid Layout Debug Borders */
.main-flex-container {
  border: 3px solid red !important;
}

.scroll-content {
  border: 3px solid blue !important;
}

.projects-grid-container {
  border: 3px solid green !important;
}

.projects-grid {
  border: 2px solid orange !important;
}

.post {
  border: 1px solid purple !important;
}

.post-content {
  border: 1px solid cyan !important;
}

.post-media {
  border: 1px solid yellow !important;
}

.post-info {
  border: 1px solid pink !important;
}

.post-title {
  border: 1px solid lime !important;
}

.post-description {
  border: 1px solid magenta !important;
}

/* Label Debug Borders */
.client-work-label-fixed,
.personal-work-label-fixed {
  border: 2px solid teal !important;
}

/* Project Detail View Debug Borders */
#project-detail-view {
  border: 3px solid red !important;
}

.project-detail-container {
  border: 3px solid blue !important;
}

.project-detail-content {
  border: 2px solid green !important;
}

.project-media-container {
  border: 2px solid orange !important;
}

.project-info-container {
  border: 2px solid purple !important;
}

/* ===== Base Layout & Background ===== */
html,
body,
.scroll-content {
  height: 100vh;
  margin: 0;
  padding: 0;
  width: 100%;
  position: relative; /* For potential pseudo-elements or for positioning child elements like .aurora. */
  overflow-x: hidden; /* Prevent horizontal scrollbars. */
  touch-action: none; /* Disable pinch-zoom and other default touch actions for a more native app feel. */
}

html {
  /* Aurora background effect */
  background: linear-gradient(to bottom, #0a0f1a, #0b1a24 60%, #0a1a1f 100%);
  background-color: #ff0000; /* Fallback color */
  line-height: 1.6; /* Default line height for text content within the app. */
  position: relative; /* Establishes a positioning context for absolutely positioned children like .aurora and .scroll-content. */
  overflow: hidden; /* Prevent aurora overflow */
}

body {
  background-color: transparent; /* Body is transparent to show the HTML element's styled background. */
  position: relative;
}

.main-flex-container {
  display: flex;
  flex-direction: row;
  height: 100vh;
  width: 100vw;
  align-items: flex-start;
  justify-content: center;
}

/* .scroll-content is the main viewport for the project feed, handling internal scrolling. */
.scroll-content {
  flex: 0 0 auto;
  height: 100%;
  margin: 0;
  position: relative; /* Needed for z-index stacking. */
  overflow-y: auto;
  z-index: 10; /* Ensures feed content is above the animated .aurora background effect. */
  background-color: transparent; /* Transparent so HTML background shows through. */
  color: #333; /* Default text color for content within the scrollable area. */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

/* Hide scrollbar for WebKit browsers (Chrome, Safari, Edge) */
.scroll-content::-webkit-scrollbar {
  display: none;
}

/* ===== Aurora Background Effect ===== */
/* This section creates the animated aurora effect inspired by the CodePen. */
.aurora {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1; /* Positioned behind everything to prevent appearing above content during transitions */
  pointer-events: none; /* Aurora is purely decorative and should not interfere with mouse events. */
  overflow: hidden;
  filter: blur(32px); /* Reduced blur for more visible bands */
}

.aurora-blob {
  position: absolute;
  border-radius: 50%;
  opacity: 0.22;
  filter: blur(48px);
  mix-blend-mode: lighten;
  will-change: transform, opacity;
  animation-timing-function: ease-in-out;
}

.blob1 {
  width: 60vw;
  height: 40vw;
  left: 5vw;
  top: -30vh;
  background: radial-gradient(circle, transparent 0%, #00ffd0 40%, #00bfff 80%, transparent 100%);
  animation: aurora-move1 18s infinite alternate;
}

.blob2 {
  width: 50vw;
  height: 30vw;
  left: 55vw;
  top: 35vh;
  background: radial-gradient(circle, transparent 0%, #00e0ff 35%, #0077ff 80%, transparent 100%);
  animation: aurora-move2 22s infinite alternate;
}

.blob3 {
  width: 40vw;
  height: 25vw;
  left: 10vw;
  top: 60vh;
  background: radial-gradient(circle, transparent 0%, #00ffb3 30%, #00ffd0 70%, transparent 100%);
  opacity: 0.18;
  animation: aurora-move3 26s infinite alternate;
}

.blob4 {
  width: 35vw;
  height: 20vw;
  left: 70vw;
  top: 10vh;
  background: radial-gradient(circle, transparent 0%, #00bfff 30%, #00e0ff 60%, transparent 100%);
  opacity: 0.15;
  animation: aurora-move4 30s infinite alternate;
}

/* New blob for top coverage */
.blob5 {
  width: 50vw;
  height: 30vw;
  left: 25vw;
  top: -40vh;
  background: radial-gradient(circle, transparent 0%, #00e0ff 30%, #00ffd0 70%, transparent 100%);
  opacity: 0.18;
  animation: aurora-move5 32s infinite alternate;
}

@keyframes aurora-move1 {
  0% { transform: translateY(0) scale(1); }
  40% { transform: translateY(50vh) scale(1.1); }
  100% { transform: translateY(100vh) scale(0.95); }
}
@keyframes aurora-move2 {
  0% { transform: translateX(0) scale(1); }
  50% { transform: translate(-30vw,-30vh) scale(1.08); }
  100% { transform: translate(-50vw,-50vh) scale(0.92); }
}
@keyframes aurora-move3 {
  0% { transform: translate(0,0) scale(1); }
  40% { transform: translate(30vw,-30vh) scale(1.12); }
  100% { transform: translate(60vw,-40vh) scale(0.9); }
}
@keyframes aurora-move4 {
  0% { transform: translate(0,0) scale(1); }
  60% { transform: translate(-20vw,30vh) scale(1.15); }
  100% { transform: translate(-40vw,100vh) scale(0.88); }
}
@keyframes aurora-move5 {
  0% { transform: translateY(0) scale(1); }
  50% { transform: translateY(40vh) scale(1.1); }
  100% { transform: translateY(80vh) scale(0.95); }
}

/* ====== Feed Container & Posts ====== */
/* .feed-container holds all the project posts and manages the full-width 2x2 masonry layout. */
.feed-container {
  position: relative;
  box-sizing: border-box;
  margin-top: 0;
  margin-bottom: 0;
  margin-left: 0;
  margin-right: 0;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  height: 100%;
  width: auto;
  max-width: 100%;
  max-height: 100%;
  left: 0;
  top: 0;
}

/* Class added by JS when projects are loaded and masonry layout is applied. */
.feed-container.loaded {
  opacity: 1;
}

/* Responsive settings for the feed container - 2x2 grid with max-width and centering. */
/* Tablet and Small Desktop: 2 columns. */
@media (min-width: 768px) {
  .feed-container {
    padding: 16px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Large Desktop: 2 columns with consistent padding. */
@media (min-width: 1200px) {
  .feed-container {
    padding: 16px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Individual project post styling. */
.post {
  border-radius: 8px;
  width: 100%;
  aspect-ratio: 1350 / 1080;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.692);
  overflow: hidden;
  cursor: pointer;
  position: relative;
  border: 2px solid transparent;
  transition:
    transform 0.25s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.25s cubic-bezier(0.4, 0, 0.2, 1),
    border-color 0.25s cubic-bezier(0.4, 0, 0.2, 1),
    z-index 0s 0.25s;
}

.post.empty-post {
  background: none !important;
  box-shadow: none !important;
  border: none !important;
  pointer-events: none;
  cursor: default;
}

.post.dimmed {
  opacity: 0.3;
  filter: grayscale(1);
  transition: opacity 0.2s, filter 0.2s;
}

/* Darkening overlay with blur effect shown on post hover. */
.post::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.75); /* Dark semi-transparent background. */
  opacity: 0; /* Hidden by default. */
  transition: opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1); /* Smooth fade-in/out. */
  pointer-events: none; /* Overlay does not intercept mouse events. */
  z-index: 1; /* Positioned above post content (img/video) but below text overlay. */
  transform: translateZ(
    0
  ); /* Promotes to its own compositing layer for smoother animations. */
  backdrop-filter: blur(8px); /* Blur effect for the background content. */
  -webkit-backdrop-filter: blur(8px); /* Safari support. */
  border-radius: 8px; /* Match the post's border radius. */
}

/* Container for all text elements shown on post hover with subtle blur effect. */
.post-text-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 8% 6%;
  box-sizing: border-box;
  text-align: center;
  opacity: 0;
  transition: opacity 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  z-index: 2;
  transform: translateZ(0);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 0.7em;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-radius: 8px;
  max-width: 100%;
  max-height: 100%;
  overflow: hidden;
  background: rgba(0,0,0,0.32);
}

/* Wrapper for main hover text (title and subtitle) to help with center alignment and grouping. */
.post-hover-text-main {
  display: flex;
  flex-direction: column;
  align-items: center; /* Center-align items. */
  justify-content: center;
  flex-grow: 0; /* Don't grow, let space-between handle distribution. */
  max-width: 80%;
}

/* Styling for the "PERSONAL WORK" / "CLIENT WORK" label on hover. */
.post-hover-project-work-label {
  font-family: "Work Sans", sans-serif;
  font-size: 0.85em;
  font-weight: 500;
  color: rgba(204, 204, 204, 0.7);
  text-transform: uppercase;
  letter-spacing: 0.08em;
  width: 100%;
  margin: 0;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 1px 4px rgba(0,0,0,0.6);
}

/* Styling for the main project title on hover. */
.post-hover-title {
  color: white;
  font-family: "Work Sans", sans-serif;
  font-size: clamp(1.7em, 3em, 2.8vh);
  font-weight: 700;
  text-shadow: 0 2px 8px rgba(0,0,0,0.7), 0 1px 3px rgba(0,0,0,0.5);
  margin: 0;
  letter-spacing: 0.04em;
  line-height: 1.1;
  padding: 0 0.2em 0.3em 0.2em;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}

/* Container for image indicator dots (shown if multiple lightbox images). */
.post-hover-image-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 0;
  flex-shrink: 0;
}

/* Individual indicator dot styling. */
.post-hover-image-dot {
  width: clamp(6px, 1.5vw, 10px); /* Responsive dot size. */
  height: clamp(6px, 1.5vw, 10px);
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.6); /* More transparent white. */
  margin: 0 clamp(2px, 0.5vw, 4px); /* Responsive spacing between dots. */
}

/* Styling for images within image-type posts in the grid. */
.post.image-post img {
  display: block; /* Remove extra space below image. */
  width: 100%; /* Image takes full width of its .post container. */
  height: auto; /* Height adjusts to maintain aspect ratio. */
}

/* Styling for videos within video-type posts in the grid. */
.post.video-post video {
  display: block; /* Remove extra space below video. */
  width: 100%; /* Video takes full width. */
  height: auto; /* Allow natural aspect ratio. */
  min-width: 100%; /* Ensure it fills width. */
  object-fit: cover; /* Maintain aspect ratio. */
  box-sizing: border-box;
}

/* Additional relative positioning for video posts if needed for specific overlays/controls on videos. */
.video-post {
  position: relative;
}

/* Enable text selection for links within posts, overriding any global user-select: none. */
.post a,
a {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

/* Apply hover effects only on devices that support true hover (e.g., mouse, not primarily touch). */
@media (hover: hover) and (pointer: fine) {
  .feed-container .post:hover {
    transform: scale(1.03); /* Slight zoom effect on hover. */
    z-index: 20; /* Bring hovered post to the front to overlap neighbors. */
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.45); /* Enhanced shadow on hover. */
    border-color: #fff; /* White border highlight on hover. */
    transition-delay:
      0s, 0s, 0s, 0s; /* Ensure all transitions happen immediately on hover, overriding any base delays. */
  }

  /* Show the ::after overlay and the text container on hover for devices supporting true hover. */
  .feed-container .post:hover::after,
  .feed-container .post:hover .post-text-container {
    opacity: 1;
  }
}

/* Responsive adjustments for text overlay elements on medium screens and up. */
@media (min-width: 768px) {
  /* Adjust vertical positioning of dots and label for better spacing on larger cards. */
  .post-hover-image-dots {
    margin-bottom: clamp(12px, 2.5vw, 20px);
  }
  .post-hover-project-work-label {
    margin-top: clamp(12px, 2.5vw, 20px);
  }
}

/* Styles to show hover overlay via JS on mobile (typically triggered by scroll). */
/* When .post has class 'hover' (added by JS), show the text and ::after overlay. */
.feed-container .post.hover .post-text-container,
.feed-container .post.hover::after {
  opacity: 1;
}

.feed-container .post.hover {
  border-color: #fff;
  z-index: 20;
  transition: border-color 0.2s;
}

/* Responsive adjustments for mobile devices (max-width: 767px). */
@media (max-width: 767px) {
  /* Adjust text container padding and alignment for mobile. */
  .post-text-container {
    justify-content: space-between; /* Space elements vertically. */
    align-items: center; /* Center items horizontally. */
    text-align: center; /* Center-align text for mobile. */
  }

  /* Mobile styling for project work label. */
  .post-hover-project-work-label {
    margin: 0; /* Remove margin, let space-between handle spacing. */
    align-self: center; /* Center-align in the flex container. */
  }

  /* Mobile styling for project title. */
  .post-hover-title {
    padding-left: 0; /* Remove horizontal padding as container is narrower. */
    padding-right: 0;
    align-self: center; /* Center-align. */
  }

  /* Mobile styling for image indicator dots. */
  .post-hover-image-dots {
    margin: 0; /* Remove margin, let space-between handle spacing. */
    justify-content: center; /* Center-align dots. */
    align-self: center; /* Center-align container. */
  }

  /* Mobile layout for the main text wrapper (which contains title and subtitle). */
  .post-hover-text-main {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    gap: 0.2em !important;
  }
  .feed-container {
    width: 100% !important;
    max-width: 100% !important;
    margin-top: 16px !important;
    padding-bottom: 16px !important;
  }
  .post {
    width: 90% !important;
    margin: 0 auto 16px auto !important;
    position: relative !important;
    left: unset !important;
    top: unset !important;
  }
  .post-text-container {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    padding: 10% 4% !important;
    background: rgba(0,0,0,0.18) !important;
    border-radius: 8px !important;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    gap: 0.7em;
    max-width: 100%;
    max-height: 100%;
    overflow: hidden;
  }
  .post-hover-title {
    font-size: 2.5em !important;
    line-height: 1.25 !important;
  }
  .post-hover-project-work-label {
    font-size: 1.05rem !important;
    line-height: 1.2 !important;
  }
  .post-hover-category-subtitle {
    font-size: 0.98rem !important;
    line-height: 1.3 !important;
  }
  /* Remove disabling of overlay on mobile */
  .post-hover-image-dot {
    width: 12px !important;
    height: 12px !important;
    margin: 0 4px;
  }
  .client-work-label-fixed,
  .personal-work-label-fixed,
  .vertical-label {
    display: none !important;
  }
}

.vertical-label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 100%;
  font-family: 'Work Sans', 'Wix Madefor Display', Arial, sans-serif;
  font-size: 1.1rem;
  font-weight: 700;
  color: #fff !important;
  letter-spacing: 0.2em;
  text-transform: uppercase;
  writing-mode: vertical-rl;
  text-orientation: mixed;
  transform: rotate(180deg);
  user-select: none;
  background: none;
  z-index: 1000;
}
.vertical-label.right {
  transform: none;
  writing-mode: vertical-lr;
  background: none;
}

.client-work-label-fixed {
  position: fixed;
  top: 50%;
  left: -48px;
  transform: translateY(-50%) rotate(-90deg);
  z-index: 10000;
  font-family: 'Work Sans', 'Wix Madefor Display', Arial, sans-serif;
  font-size: clamp(1.1rem, 2.5vw, 2.5rem);
  font-weight: 700;
  color: #b8e0ff40;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  padding: 4px 16px;
  border-radius: 8px;
  pointer-events: auto;
  user-select: none;
  transition: color 0.2s;
}

.client-work-label-fixed:hover {
  color: #b8e0ff;
}

.personal-work-label-fixed {
  position: fixed;
  top: 50%;
  right: -74px;
  transform: translateY(-50%) rotate(90deg);
  z-index: 10000;
  font-family: 'Work Sans', 'Wix Madefor Display', Arial, sans-serif;
  font-size: clamp(1.1rem, 2.5vw, 2.5rem);
  font-weight: 700;
  color: #b8e0ff40;
  letter-spacing: 0.04em;
  text-transform: uppercase;
  padding: 4px 16px;
  border-radius: 8px;
  pointer-events: auto;
  user-select: none;
  transition: color 0.2s;
}

.personal-work-label-fixed:hover {
  color: #b8e0ff;
}

.post-hover-project-work-label {
  font-size: 1.2em;
  font-weight: 700;
  color: #444;
  text-shadow: 0 1px 4px rgba(0,0,0,0.6);
  margin: 0;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.post-hover-category-subtitle {
  font-family: "Work Sans", sans-serif;
  font-size: clamp(0.8rem, 1.2rem, 1.1vh);
  font-weight: 500;
  color: rgba(204,204,204,0.85);
  line-height: 1.3;
  margin: 0;
  letter-spacing: 0.04em;
  text-shadow: none;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
}

.post-hover-project-work-label,
.post-hover-title,
.post-hover-image-dots {
  margin: 0;
}

.post-hover-title {
  text-shadow: 0 2px 8px rgba(0,0,0,0.7), 0 1px 3px rgba(0,0,0,0.5);
}

.post-hover-project-work-label {
  text-shadow: 0 1px 4px rgba(0,0,0,0.6);
}

@media (min-width: 768px) {
  .post-hover-title {
    font-size: clamp(1.7em, 3.5vh, 3.5em);
  }
}

.client-work-label-fixed,
.personal-work-label-fixed {
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (max-width: 879px) {
  .client-work-label-fixed,
  .personal-work-label-fixed {
    opacity: 0;
    pointer-events: none;
  }
}

/* ===== Project Detail View Styles ===== */

/* CSS Custom Properties for scrollbar width calculation */
:root {
  --scrollbar-width: 0px;
}















/* View transitions */
#projects-grid-view {
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#projects-grid-view.hidden {
  opacity: 0;
  pointer-events: none;
}

/* ===== CUSTOM SCROLL INDICATOR ===== */
.scroll-indicator {
  position: fixed;
  top: 10px;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1000;
  pointer-events: none;
  border: 2px solid red !important; /* DEBUG BORDER */
}

.scroll-dot {
  position: absolute;
  top: -2px;
  left: 0;
  width: 8px;
  height: 8px;
  background: #ffffff;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  transition: left 0.1s ease-out;
  border: 2px solid lime !important; /* DEBUG BORDER */
}

/* ===== HIDE SCROLLBARS ===== */
.scroll-content {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.scroll-content::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Hide scrollbars on body and html as well */
body {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

body::-webkit-scrollbar {
  display: none;
}

html {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

html::-webkit-scrollbar {
  display: none;
}

/* ===== PROJECT DETAIL PAGE ===== */
.project-detail-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  z-index: 2000;
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-detail-page.visible {
  opacity: 1;
}

/* Detail page scroll content */
.detail-scroll-content {
  position: absolute;
  top: 20px; /* Space for scroll indicator */
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.detail-scroll-content::-webkit-scrollbar {
  display: none;
}

/* Detail content container */
.detail-content-container {
  min-height: 100%;
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
  border: 2px solid blue !important; /* DEBUG BORDER */
}

/* Detail page scroll indicator */
.detail-scroll-indicator {
  position: absolute;
  top: 10px;
  left: 0;
  right: 0;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  z-index: 2100;
  pointer-events: none;
  border: 2px solid orange !important; /* DEBUG BORDER */
}
